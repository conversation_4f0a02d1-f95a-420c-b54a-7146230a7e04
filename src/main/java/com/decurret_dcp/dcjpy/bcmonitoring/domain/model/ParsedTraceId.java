package com.decurret_dcp.dcjpy.bcmonitoring.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ParsedTraceId {
  @JsonProperty("traceId")
  private byte[] traceId;

  @JsonProperty("providerEoa")
  private String providerEoa;

  public byte[] getTraceId() {
    return traceId;
  }

  public void setTraceId(byte[] traceId) {
    this.traceId = traceId;
  }

  public String getProviderEoa() {
    return providerEoa;
  }

  public void setProviderEoa(String providerEoa) {
    this.providerEoa = providerEoa;
  }
}
